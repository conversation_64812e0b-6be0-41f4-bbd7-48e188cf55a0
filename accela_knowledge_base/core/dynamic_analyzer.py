"""
Dynamic County Data Analyzer for Accela Knowledge Base
Flexible analyzer that can examine any aspect of county implementations
"""

import os
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryAnalysis
from ..llm.llm_helper import LLMHelper
from ..data.metadata_extractor import MetadataExtractor


@dataclass
class CountyAnalysisResult:
    """Result of analyzing a county's implementation"""
    county: str
    analysis_type: str
    findings: Dict[str, Any]
    code_examples: List[Dict[str, str]]
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class ComparisonResult:
    """Result of comparing multiple counties"""
    counties: List[str]
    analysis_type: str
    comparison_matrix: Dict[str, Dict[str, Any]]
    similarities: List[str]
    differences: List[Dict[str, Any]]
    best_practices: List[Dict[str, Any]]
    recommendations: List[str]
    summary: str


class DynamicCountyAnalyzer(LoggerMixin):
    """
    Dynamic analyzer that can examine county implementations for any aspect
    requested by the user, using LLM intelligence to understand what to look for
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        self.metadata_extractor = MetadataExtractor(config)
        
        # Load county metadata
        self.county_metadata = self._load_county_metadata()
        
        self.logger.info("Dynamic county analyzer initialized")
    
    def analyze_counties(self, query_analysis: DynamicQueryAnalysis) -> List[CountyAnalysisResult]:
        """
        Analyze counties based on dynamic query analysis
        
        Args:
            query_analysis: Result from DynamicQueryProcessor
            
        Returns:
            List of CountyAnalysisResult for each relevant county
        """
        
        self.logger.info(f"Analyzing counties for: {query_analysis.intent}")
        
        # Determine which counties to analyze
        target_counties = self._determine_target_counties(query_analysis)
        
        results = []
        for county in target_counties:
            try:
                result = self._analyze_single_county(county, query_analysis)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to analyze {county}: {e}")
        
        return results
    
    def compare_counties(self, query_analysis: DynamicQueryAnalysis, 
                        county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """
        Compare multiple counties based on analysis results
        
        Args:
            query_analysis: Original query analysis
            county_results: Individual county analysis results
            
        Returns:
            ComparisonResult with detailed comparison
        """
        
        if len(county_results) < 2:
            self.logger.warning("Need at least 2 counties for comparison")
            return self._create_empty_comparison(query_analysis)
        
        self.logger.info(f"Comparing {len(county_results)} counties")
        
        if self.llm_helper:
            return self._compare_with_llm(query_analysis, county_results)
        else:
            return self._compare_with_fallback(query_analysis, county_results)
    
    def _analyze_single_county(self, county: str, 
                              query_analysis: DynamicQueryAnalysis) -> Optional[CountyAnalysisResult]:
        """Analyze a single county's implementation"""
        
        # Get county metadata and scripts
        county_data = self.county_metadata.get(county, {})
        if not county_data:
            self.logger.warning(f"No data found for county: {county}")
            return None
        
        # Get relevant scripts based on query analysis
        relevant_scripts = self._find_relevant_scripts(county_data, query_analysis)
        
        if self.llm_helper:
            return self._analyze_with_llm(county, relevant_scripts, query_analysis)
        else:
            return self._analyze_with_fallback(county, relevant_scripts, query_analysis)
    
    def _analyze_with_llm(self, county: str, scripts: List[Dict], 
                         query_analysis: DynamicQueryAnalysis) -> CountyAnalysisResult:
        """Analyze county using LLM intelligence"""
        
        try:
            # Build analysis prompt
            prompt = self._build_analysis_prompt(county, scripts, query_analysis)
            
            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2000,
                temperature=0.2
            )
            
            # Parse LLM analysis
            llm_result = self._parse_llm_county_analysis(response.choices[0].message.content)
            
            return CountyAnalysisResult(
                county=county,
                analysis_type=query_analysis.analysis_type,
                findings=llm_result.get('findings', {}),
                code_examples=llm_result.get('code_examples', []),
                strengths=llm_result.get('strengths', []),
                weaknesses=llm_result.get('weaknesses', []),
                recommendations=llm_result.get('recommendations', []),
                confidence=llm_result.get('confidence', 0.8),
                metadata={'scripts_analyzed': len(scripts)}
            )
            
        except Exception as e:
            self.logger.error(f"LLM analysis failed for {county}: {e}")
            return self._analyze_with_fallback(county, scripts, query_analysis)
    
    def _build_analysis_prompt(self, county: str, scripts: List[Dict], 
                              query_analysis: DynamicQueryAnalysis) -> str:
        """Build comprehensive analysis prompt for LLM"""
        
        # Prepare script information
        script_info = []
        for script in scripts[:5]:  # Limit to top 5 most relevant
            script_info.append({
                'path': script.get('file_path', ''),
                'functions': script.get('functions', [])[:10],  # Top 10 functions
                'content_preview': script.get('content', '')[:1000]  # First 1000 chars
            })
        
        return f"""
You are an expert Accela implementation analyst. Analyze {county}'s implementation based on this query.

ORIGINAL QUERY: "{query_analysis.original_query}"

ANALYSIS TYPE: {query_analysis.analysis_type}

SPECIFIC ASPECTS TO ANALYZE: {', '.join(query_analysis.specific_aspects)}

COUNTY: {county}

RELEVANT SCRIPTS:
{json.dumps(script_info, indent=2)}

Please provide a comprehensive analysis in JSON format with:

1. "findings": {{
   "key_patterns": [list of important patterns found],
   "implementation_approach": "description of how this county implements the requested functionality",
   "technical_details": [specific technical findings],
   "business_logic": [business process insights]
}}

2. "code_examples": [
   {{
     "description": "what this code does",
     "code": "actual code snippet",
     "file": "source file path",
     "significance": "why this is important"
   }}
]

3. "strengths": [list of what this county does well]

4. "weaknesses": [list of areas for improvement]

5. "recommendations": [specific actionable recommendations]

6. "confidence": 0.0-1.0 confidence in this analysis

Focus specifically on: {', '.join(query_analysis.specific_aspects)}

Respond ONLY with valid JSON.
"""
    
    def _analyze_with_fallback(self, county: str, scripts: List[Dict], 
                              query_analysis: DynamicQueryAnalysis) -> CountyAnalysisResult:
        """Fallback analysis when LLM is not available"""
        
        findings = {}
        code_examples = []
        strengths = []
        weaknesses = []
        recommendations = []
        
        # Basic pattern analysis
        for script in scripts[:3]:  # Analyze top 3 scripts
            content = script.get('content', '')
            functions = script.get('functions', [])
            
            # Extract code examples
            if content:
                code_examples.append({
                    'description': f"Implementation from {script.get('file_path', 'unknown')}",
                    'code': content[:500] + '...' if len(content) > 500 else content,
                    'file': script.get('file_path', ''),
                    'significance': 'Key implementation pattern'
                })
            
            # Basic strengths/weaknesses analysis
            if len(functions) > 5:
                strengths.append(f"Rich functionality with {len(functions)} functions")
            elif len(functions) < 2:
                weaknesses.append("Limited functionality")
            
            if 'error' in content.lower() or 'try' in content.lower():
                strengths.append("Includes error handling")
            else:
                weaknesses.append("May lack comprehensive error handling")
        
        # Basic findings
        findings = {
            'key_patterns': [f"Found {len(scripts)} relevant scripts"],
            'implementation_approach': f"{county} uses standard Accela patterns",
            'technical_details': [f"Analyzed {len(scripts)} scripts"],
            'business_logic': ["Standard government workflow patterns"]
        }
        
        # Basic recommendations
        recommendations = [
            f"Review {county}'s implementation for best practices",
            "Consider code optimization opportunities",
            "Ensure comprehensive testing"
        ]
        
        return CountyAnalysisResult(
            county=county,
            analysis_type=query_analysis.analysis_type,
            findings=findings,
            code_examples=code_examples,
            strengths=strengths,
            weaknesses=weaknesses,
            recommendations=recommendations,
            confidence=0.6,
            metadata={'scripts_analyzed': len(scripts)}
        )
    
    def _compare_with_llm(self, query_analysis: DynamicQueryAnalysis, 
                         county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """Compare counties using LLM intelligence"""
        
        try:
            # Build comparison prompt
            prompt = self._build_comparison_prompt(query_analysis, county_results)
            
            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2500,
                temperature=0.2
            )
            
            # Parse LLM comparison
            llm_comparison = self._parse_llm_comparison(response.choices[0].message.content)
            
            return ComparisonResult(
                counties=[r.county for r in county_results],
                analysis_type=query_analysis.analysis_type,
                comparison_matrix=llm_comparison.get('comparison_matrix', {}),
                similarities=llm_comparison.get('similarities', []),
                differences=llm_comparison.get('differences', []),
                best_practices=llm_comparison.get('best_practices', []),
                recommendations=llm_comparison.get('recommendations', []),
                summary=llm_comparison.get('summary', '')
            )
            
        except Exception as e:
            self.logger.error(f"LLM comparison failed: {e}")
            return self._compare_with_fallback(query_analysis, county_results)
    
    def _build_comparison_prompt(self, query_analysis: DynamicQueryAnalysis, 
                                county_results: List[CountyAnalysisResult]) -> str:
        """Build comparison prompt for LLM"""
        
        # Prepare county data for comparison
        county_data = []
        for result in county_results:
            county_data.append({
                'county': result.county,
                'findings': result.findings,
                'strengths': result.strengths,
                'weaknesses': result.weaknesses,
                'code_examples': [ex['description'] for ex in result.code_examples]
            })
        
        return f"""
You are an expert Accela implementation analyst. Compare these county implementations.

ORIGINAL QUERY: "{query_analysis.original_query}"

ANALYSIS TYPE: {query_analysis.analysis_type}

COUNTY ANALYSIS RESULTS:
{json.dumps(county_data, indent=2)}

Please provide a comprehensive comparison in JSON format with:

1. "comparison_matrix": {{
   "county1": {{
     "approach": "description",
     "complexity": "high/medium/low",
     "effectiveness": "high/medium/low",
     "maintainability": "high/medium/low"
   }},
   "county2": {{ ... }}
}}

2. "similarities": [list of common approaches/patterns across counties]

3. "differences": [
   {{
     "aspect": "what differs",
     "county1_approach": "how county1 does it",
     "county2_approach": "how county2 does it",
     "impact": "significance of this difference"
   }}
]

4. "best_practices": [
   {{
     "practice": "description of best practice",
     "county": "which county demonstrates this",
     "why": "why this is a best practice"
   }}
]

5. "recommendations": [actionable recommendations based on comparison]

6. "summary": "concise summary of key findings and recommendations"

Respond ONLY with valid JSON.
"""

    def _compare_with_fallback(self, query_analysis: DynamicQueryAnalysis,
                              county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """Fallback comparison when LLM is not available"""

        counties = [r.county for r in county_results]

        # Basic comparison matrix
        comparison_matrix = {}
        for result in county_results:
            comparison_matrix[result.county] = {
                'approach': f"Standard {result.analysis_type}",
                'complexity': 'medium',
                'effectiveness': 'medium',
                'maintainability': 'medium'
            }

        # Find similarities
        similarities = []
        all_strengths = [strength for result in county_results for strength in result.strengths]
        common_strengths = set()
        for strength in all_strengths:
            if all_strengths.count(strength) > 1:
                common_strengths.add(strength)
        similarities.extend(list(common_strengths))

        # Find differences
        differences = []
        for i, result1 in enumerate(county_results):
            for j, result2 in enumerate(county_results[i+1:], i+1):
                unique_strengths1 = set(result1.strengths) - set(result2.strengths)
                unique_strengths2 = set(result2.strengths) - set(result1.strengths)

                if unique_strengths1 or unique_strengths2:
                    differences.append({
                        'aspect': 'implementation_strengths',
                        'county1_approach': f"{result1.county}: {', '.join(unique_strengths1)}",
                        'county2_approach': f"{result2.county}: {', '.join(unique_strengths2)}",
                        'impact': 'Different implementation focuses'
                    })

        # Basic best practices
        best_practices = []
        for result in county_results:
            if result.confidence > 0.8:
                best_practices.append({
                    'practice': f"High-confidence implementation in {result.county}",
                    'county': result.county,
                    'why': f"Analysis confidence: {result.confidence:.2f}"
                })

        # Basic recommendations
        recommendations = [
            "Compare implementation approaches across counties",
            "Identify and adopt best practices from high-performing counties",
            "Consider standardizing common patterns"
        ]

        summary = f"Compared {len(counties)} counties for {query_analysis.analysis_type}. Found {len(similarities)} common patterns and {len(differences)} key differences."

        return ComparisonResult(
            counties=counties,
            analysis_type=query_analysis.analysis_type,
            comparison_matrix=comparison_matrix,
            similarities=similarities,
            differences=differences,
            best_practices=best_practices,
            recommendations=recommendations,
            summary=summary
        )

    def _determine_target_counties(self, query_analysis: DynamicQueryAnalysis) -> List[str]:
        """Determine which counties to analyze based on query"""

        # If specific counties mentioned, use those
        counties_from_entities = query_analysis.entities.get('counties', [])

        # Filter out invalid county names (like "string" or empty values)
        valid_counties = []
        for county in counties_from_entities:
            if isinstance(county, str) and county.strip() and county != "string":
                # Check if county exists in our metadata
                if county in self.county_metadata:
                    valid_counties.append(county)
                else:
                    # Try to find a matching county name
                    county_lower = county.lower().replace(' ', '_')
                    for available_county in self.county_metadata.keys():
                        if available_county.lower() == county_lower:
                            valid_counties.append(available_county)
                            break

        if valid_counties:
            self.logger.info(f"Using specific counties: {valid_counties}")
            return valid_counties

        # For comparison queries without specific counties, use a representative sample
        if query_analysis.comparison_requested or 'compare' in query_analysis.intent.lower():
            # Use a diverse set of counties for comparison
            sample_counties = ['asheville', 'santa_barbara', 'marin', 'dayton', 'leon']
            available_sample = [c for c in sample_counties if c in self.county_metadata]
            if available_sample:
                self.logger.info(f"Using sample counties for comparison: {available_sample}")
                return available_sample[:3]  # Limit to 3 for performance

        # Otherwise, return all available counties (but limit for performance)
        all_counties = list(self.county_metadata.keys())
        if len(all_counties) > 5:
            self.logger.info(f"Limiting analysis to first 5 counties: {all_counties[:5]}")
            return all_counties[:5]

        self.logger.info(f"Using all available counties: {all_counties}")
        return all_counties

    def _find_relevant_scripts(self, county_data: Dict,
                              query_analysis: DynamicQueryAnalysis) -> List[Dict]:
        """Find scripts relevant to the query analysis"""

        scripts = county_data.get('scripts', [])
        relevant_scripts = []

        # Score scripts based on relevance to query
        for script in scripts:
            score = self._calculate_script_relevance(script, query_analysis)
            if score > 0:
                script['relevance_score'] = score
                relevant_scripts.append(script)

        # Sort by relevance and return top scripts
        relevant_scripts.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        return relevant_scripts[:10]  # Top 10 most relevant

    def _calculate_script_relevance(self, script: Dict,
                                   query_analysis: DynamicQueryAnalysis) -> float:
        """Calculate how relevant a script is to the query"""

        score = 0.0
        content = script.get('content', '').lower()
        functions = script.get('functions', [])
        file_path = script.get('file_path', '').lower()

        # Check for technical concepts
        for concept in query_analysis.entities.get('technical_concepts', []):
            if concept in content:
                score += 0.3
            if concept in file_path:
                score += 0.2

        # Check for specific terms
        for term in query_analysis.entities.get('specific_terms', []):
            if term.lower() in content:
                score += 0.4

        # Check for specific aspects
        for aspect in query_analysis.specific_aspects:
            if aspect.lower() in content:
                score += 0.5
            if aspect.lower() in file_path:
                score += 0.3

        # Function relevance
        for func in functions:
            func_name = func.lower() if isinstance(func, str) else str(func).lower()
            for concept in query_analysis.entities.get('technical_concepts', []):
                if concept in func_name:
                    score += 0.2

        return score

    def _load_county_metadata(self) -> Dict[str, Dict]:
        """Load county metadata from the metadata extractor"""

        try:
            # Extract metadata for all counties
            all_scripts = self.metadata_extractor.extract_all()

            # Group by county
            county_data = {}
            for script in all_scripts:
                county = script.county
                if county not in county_data:
                    county_data[county] = {'scripts': []}

                # Convert script metadata to dict
                script_dict = {
                    'file_path': script.file_path,
                    'county': script.county,
                    'script_type': script.script_type,
                    'functions': script.functions,
                    'dependencies': script.dependencies,
                    'complexity': script.complexity,
                    'content': script.content or ''
                }

                county_data[county]['scripts'].append(script_dict)

            self.logger.info(f"Loaded metadata for {len(county_data)} counties: {list(county_data.keys())}")
            for county, data in county_data.items():
                self.logger.info(f"County {county}: {len(data['scripts'])} scripts")
            return county_data

        except Exception as e:
            self.logger.error(f"Failed to load county metadata: {e}")
            return {}

    def _parse_llm_county_analysis(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM county analysis response"""

        try:
            import re
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            else:
                return {}
        except json.JSONDecodeError:
            return {}

    def _parse_llm_comparison(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM comparison response"""

        try:
            import re
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            else:
                return {}
        except json.JSONDecodeError:
            return {}

    def _create_empty_comparison(self, query_analysis: DynamicQueryAnalysis) -> ComparisonResult:
        """Create empty comparison result when insufficient data"""

        return ComparisonResult(
            counties=[],
            analysis_type=query_analysis.analysis_type,
            comparison_matrix={},
            similarities=[],
            differences=[],
            best_practices=[],
            recommendations=["Insufficient data for comparison"],
            summary="Not enough county data available for comparison"
        )
