"""
Dynamic Response Generator for Accela Knowledge Base
Uses LLM intelligence to create comprehensive, contextual answers in markdown format
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryAnalysis
from .dynamic_analyzer import CountyAnalysisResult, ComparisonResult
from ..llm.llm_helper import LLMHelper


class DynamicResponseGenerator(LoggerMixin):
    """
    Generates comprehensive, contextual responses in markdown format
    based on dynamic analysis results using LLM intelligence
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        
        self.logger.info("Dynamic response generator initialized")
    
    def generate_response(self, 
                         query_analysis: DynamicQueryAnalysis,
                         county_results: List[CountyAnalysisResult],
                         comparison_result: Optional[ComparisonResult] = None) -> str:
        """
        Generate comprehensive markdown response based on analysis results
        
        Args:
            query_analysis: Original query analysis
            county_results: Individual county analysis results
            comparison_result: Optional comparison result if multiple counties
            
        Returns:
            Comprehensive markdown response
        """
        
        self.logger.info(f"Generating response for: {query_analysis.intent}")
        
        if self.llm_helper:
            return self._generate_with_llm(query_analysis, county_results, comparison_result)
        else:
            return self._generate_with_fallback(query_analysis, county_results, comparison_result)
    
    def _generate_with_llm(self, 
                          query_analysis: DynamicQueryAnalysis,
                          county_results: List[CountyAnalysisResult],
                          comparison_result: Optional[ComparisonResult]) -> str:
        """Generate response using LLM intelligence"""
        
        try:
            # Build comprehensive prompt
            prompt = self._build_response_prompt(query_analysis, county_results, comparison_result)
            
            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=3000,
                temperature=0.3  # Slightly higher for more natural language
            )
            
            markdown_response = response.choices[0].message.content.strip()
            
            # Add metadata footer
            markdown_response += self._generate_metadata_footer(query_analysis, county_results)
            
            self.logger.info("LLM response generated successfully")
            return markdown_response
            
        except Exception as e:
            self.logger.error(f"LLM response generation failed: {e}")
            return self._generate_with_fallback(query_analysis, county_results, comparison_result)
    
    def _build_response_prompt(self, 
                              query_analysis: DynamicQueryAnalysis,
                              county_results: List[CountyAnalysisResult],
                              comparison_result: Optional[ComparisonResult]) -> str:
        """Build comprehensive prompt for response generation"""
        
        # Prepare data for prompt
        county_data = []
        for result in county_results:
            county_data.append({
                'county': result.county,
                'findings': result.findings,
                'code_examples': result.code_examples[:3],  # Top 3 examples
                'strengths': result.strengths,
                'weaknesses': result.weaknesses,
                'recommendations': result.recommendations
            })
        
        comparison_data = {}
        if comparison_result:
            comparison_data = {
                'similarities': comparison_result.similarities,
                'differences': comparison_result.differences[:5],  # Top 5 differences
                'best_practices': comparison_result.best_practices,
                'summary': comparison_result.summary
            }
        
        return f"""
You are an expert Accela implementation consultant. Create a comprehensive, professional markdown response to answer the user's query.

ORIGINAL QUERY: "{query_analysis.original_query}"

QUERY INTENT: {query_analysis.intent}

ANALYSIS TYPE: {query_analysis.analysis_type}

COUNTY ANALYSIS RESULTS:
{json.dumps(county_data, indent=2)}

COMPARISON RESULTS (if applicable):
{json.dumps(comparison_data, indent=2) if comparison_data else "No comparison performed"}

OUTPUT REQUIREMENTS:
- Format: Professional markdown
- Include code examples where relevant
- Detail level: {query_analysis.output_requirements.get('detail_level', 'high')}
- Include examples: {query_analysis.output_requirements.get('include_examples', True)}
- Include code: {query_analysis.output_requirements.get('include_code', True)}

Please create a comprehensive response that:

1. **Directly answers the user's question** with a clear, concise summary
2. **Provides detailed findings** for each county analyzed
3. **Includes relevant code examples** with explanations
4. **Highlights key differences** between counties (if comparison was performed)
5. **Identifies best practices** and recommendations
6. **Uses professional markdown formatting** with headers, bullet points, code blocks
7. **Focuses on actionable insights** that help with Accela implementation decisions

Structure your response with:
- Executive Summary
- Detailed Findings (by county)
- Code Examples (if relevant)
- Comparison Analysis (if multiple counties)
- Best Practices & Recommendations
- Next Steps

Use markdown code blocks for code examples with appropriate language tags (javascript, sql, etc.).
Make the response comprehensive but well-organized and easy to read.
"""
    
    def _generate_with_fallback(self, 
                               query_analysis: DynamicQueryAnalysis,
                               county_results: List[CountyAnalysisResult],
                               comparison_result: Optional[ComparisonResult]) -> str:
        """Generate response using fallback method when LLM is not available"""
        
        markdown_parts = []
        
        # Header
        markdown_parts.append(f"# Analysis Results: {query_analysis.original_query}")
        markdown_parts.append("")
        
        # Executive Summary
        markdown_parts.append("## Executive Summary")
        markdown_parts.append("")
        
        if len(county_results) == 1:
            result = county_results[0]
            markdown_parts.append(f"Analysis of **{result.county}**'s implementation for {query_analysis.analysis_type}.")
            markdown_parts.append(f"Found {len(result.code_examples)} relevant code examples and {len(result.strengths)} key strengths.")
        else:
            markdown_parts.append(f"Comparative analysis of **{len(county_results)} counties** for {query_analysis.analysis_type}.")
            if comparison_result:
                markdown_parts.append(f"Identified {len(comparison_result.similarities)} common patterns and {len(comparison_result.differences)} key differences.")
        
        markdown_parts.append("")
        
        # County Details
        for result in county_results:
            # Format county name properly
            county_display_name = self._format_county_name(result.county)
            markdown_parts.append(f"## {county_display_name} Implementation")
            markdown_parts.append("")
            
            # Findings
            if result.findings:
                markdown_parts.append("### Key Findings")
                for key, value in result.findings.items():
                    if isinstance(value, list):
                        markdown_parts.append(f"**{key.replace('_', ' ').title()}:**")
                        for item in value[:3]:  # Top 3 items
                            markdown_parts.append(f"- {item}")
                    else:
                        markdown_parts.append(f"**{key.replace('_', ' ').title()}:** {value}")
                markdown_parts.append("")
            
            # Code Examples
            if result.code_examples:
                markdown_parts.append("### Code Examples")
                for i, example in enumerate(result.code_examples[:2]):  # Top 2 examples
                    markdown_parts.append(f"#### {example.get('description', f'Example {i+1}')}")
                    markdown_parts.append("```javascript")
                    markdown_parts.append(example.get('code', '// No code available'))
                    markdown_parts.append("```")
                    if example.get('significance'):
                        markdown_parts.append(f"*{example['significance']}*")
                    markdown_parts.append("")
            
            # Strengths and Weaknesses
            if result.strengths:
                markdown_parts.append("### Strengths")
                for strength in result.strengths:
                    markdown_parts.append(f"✅ {strength}")
                markdown_parts.append("")
            
            if result.weaknesses:
                markdown_parts.append("### Areas for Improvement")
                for weakness in result.weaknesses:
                    markdown_parts.append(f"⚠️ {weakness}")
                markdown_parts.append("")
        
        # Comparison Analysis
        if comparison_result and len(county_results) > 1:
            markdown_parts.append("## Comparison Analysis")
            markdown_parts.append("")
            
            if comparison_result.similarities:
                markdown_parts.append("### Common Patterns")
                for similarity in comparison_result.similarities:
                    markdown_parts.append(f"- {similarity}")
                markdown_parts.append("")
            
            if comparison_result.differences:
                markdown_parts.append("### Key Differences")
                for diff in comparison_result.differences[:3]:  # Top 3 differences
                    markdown_parts.append(f"**{diff.get('aspect', 'Difference')}:**")
                    markdown_parts.append(f"- {diff.get('county1_approach', 'N/A')}")
                    markdown_parts.append(f"- {diff.get('county2_approach', 'N/A')}")
                    markdown_parts.append("")
        
        # Recommendations
        markdown_parts.append("## Recommendations")
        markdown_parts.append("")
        
        all_recommendations = []
        for result in county_results:
            all_recommendations.extend(result.recommendations)
        
        if comparison_result:
            all_recommendations.extend(comparison_result.recommendations)
        
        # Remove duplicates and add to markdown
        unique_recommendations = list(set(all_recommendations))
        for rec in unique_recommendations[:5]:  # Top 5 recommendations
            markdown_parts.append(f"- {rec}")
        
        markdown_parts.append("")
        
        # Add metadata footer
        markdown_parts.append(self._generate_metadata_footer(query_analysis, county_results))
        
        return "\n".join(markdown_parts)

    def _format_county_name(self, county_code: str) -> str:
        """Format county code into a readable display name"""

        # County name mappings for better display
        county_display_names = {
            'asheville': 'Asheville',
            'santa_barbara': 'Santa Barbara County',
            'marin': 'Marin County',
            'san_mateo': 'San Mateo County',
            'alameda': 'Alameda County',
            'contra_costa': 'Contra Costa County',
            'sonoma': 'Sonoma County',
            'napa': 'Napa County',
            'solano': 'Solano County',
            'mendocino': 'Mendocino County',
            'dayton': 'Dayton',
            'leon': 'Leon County',
            'atlanta_chattanooga': 'Atlanta Chattanooga',
            'lancaster': 'Lancaster County',
            'cok': 'City of Kirkland',
            'ep_support': 'EP Support',
        }

        return county_display_names.get(county_code, county_code.title().replace('_', ' '))
    
    def _generate_metadata_footer(self, 
                                 query_analysis: DynamicQueryAnalysis,
                                 county_results: List[CountyAnalysisResult]) -> str:
        """Generate metadata footer for the response"""
        
        footer_parts = [
            "",
            "---",
            "",
            "### Analysis Metadata",
            "",
            f"**Query Type:** {query_analysis.analysis_type}",
            f"**Counties Analyzed:** {', '.join([r.county for r in county_results])}",
            f"**Analysis Confidence:** {query_analysis.confidence:.1%}",
            f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "*Generated by Accela Knowledge Base - Dynamic AI Analysis System*"
        ]
        
        return "\n".join(footer_parts)
